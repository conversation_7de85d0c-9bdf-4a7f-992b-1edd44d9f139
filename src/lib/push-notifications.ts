import { upsertPoint, searchByPayload } from './db';
import type { NotificationSubscription } from './types';

// VAPID public key - should match the one in the API endpoint
const VAPID_PUBLIC_KEY = 'BEl62iUYgUivxIkv69yViEuiBIa40HcCWLEaQK07x8hiKSHjfcHqLm1kZHLQjF4rXYJd4BPZ09lS1P9_4M4CsUg';

export class PushNotificationManager {
  private registration: ServiceWorkerRegistration | null = null;

  async initialize(): Promise<boolean> {
    if (!('serviceWorker' in navigator)) {
      console.warn('Service workers not supported');
      return false;
    }

    if (!('PushManager' in window)) {
      console.warn('Push messaging not supported');
      return false;
    }

    try {
      // Register service worker
      this.registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service worker registered:', this.registration);
      return true;
    } catch (error) {
      console.error('Service worker registration failed:', error);
      return false;
    }
  }

  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('Notifications not supported');
    }

    const permission = await Notification.requestPermission();
    return permission;
  }

  async subscribe(userId: string, schoolId: string): Promise<PushSubscription | null> {
    if (!this.registration) {
      throw new Error('Service worker not registered');
    }

    try {
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(VAPID_PUBLIC_KEY)
      });

      // Save subscription to database
      await this.saveSubscription(subscription, userId, schoolId);

      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      throw error;
    }
  }

  async unsubscribe(userId: string, schoolId: string): Promise<boolean> {
    if (!this.registration) {
      return false;
    }

    try {
      const subscription = await this.registration.pushManager.getSubscription();
      if (subscription) {
        await subscription.unsubscribe();
        
        // Remove from database
        await this.removeSubscription(subscription.endpoint, userId, schoolId);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }

  async getSubscription(): Promise<PushSubscription | null> {
    if (!this.registration) {
      return null;
    }

    return await this.registration.pushManager.getSubscription();
  }

  async isSubscribed(userId: string, schoolId: string): Promise<boolean> {
    const subscription = await this.getSubscription();
    if (!subscription) return false;

    // Check if subscription exists in database
    const dbSubscriptions = await searchByPayload<NotificationSubscription>({
      s: 'n',
      u: userId,
      sc: schoolId,
      e: subscription.endpoint,
      ac: true
    });

    return dbSubscriptions.length > 0;
  }

  private async saveSubscription(
    subscription: PushSubscription, 
    userId: string, 
    schoolId: string
  ): Promise<void> {
    const keys = subscription.getKey ? {
      p256dh: subscription.getKey('p256dh'),
      auth: subscription.getKey('auth')
    } : { p256dh: null, auth: null };

    const notificationSubscription: NotificationSubscription = {
      s: 'n',
      u: userId,
      e: subscription.endpoint,
      k: keys.p256dh ? this.arrayBufferToBase64(keys.p256dh) : '',
      a: keys.auth ? this.arrayBufferToBase64(keys.auth) : '',
      sc: schoolId,
      ac: true
    };

    await upsertPoint(notificationSubscription);
  }

  private async removeSubscription(
    endpoint: string, 
    userId: string, 
    schoolId: string
  ): Promise<void> {
    // Find and deactivate the subscription
    const subscriptions = await searchByPayload<NotificationSubscription>({
      s: 'n',
      u: userId,
      sc: schoolId,
      e: endpoint
    });

    for (const subscription of subscriptions) {
      if (subscription.id) {
        await upsertPoint({
          ...subscription,
          ac: false // Deactivate instead of deleting
        });
      }
    }
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }
}

// Utility function to send push notifications
export async function sendPushNotification(
  userId: string,
  schoolId: string,
  message: string,
  title?: string,
  type?: string
): Promise<Response> {
  const response = await fetch('/api/push', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      userId,
      schoolId,
      message,
      title,
      type
    })
  });

  return response;
}

// Create singleton instance
export const pushNotificationManager = new PushNotificationManager();
