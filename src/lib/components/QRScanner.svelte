<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { createEventDispatcher } from 'svelte';
  import jsQR from 'jsqr';

  export let isActive = false;
  export let facingMode: 'user' | 'environment' = 'environment';

  const dispatch = createEventDispatcher<{
    scan: { data: string };
    error: { message: string };
  }>();

  let video: HTMLVideoElement;
  let canvas: HTMLCanvasElement;
  let stream: MediaStream | null = null;
  let scanning = false;
  let animationFrame: number;

  onMount(() => {
    if (isActive) {
      startScanning();
    }
  });

  onDestroy(() => {
    stopScanning();
  });

  $: if (isActive) {
    startScanning();
  } else {
    stopScanning();
  }

  async function startScanning() {
    if (scanning) return;
    
    try {
      scanning = true;
      
      // Request camera access
      stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode,
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      if (video) {
        video.srcObject = stream;
        video.play();
        
        video.addEventListener('loadedmetadata', () => {
          scanFrame();
        });
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      dispatch('error', { message: 'Failed to access camera. Please ensure camera permissions are granted.' });
      scanning = false;
    }
  }

  function stopScanning() {
    scanning = false;
    
    if (animationFrame) {
      cancelAnimationFrame(animationFrame);
    }
    
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      stream = null;
    }
  }

  function scanFrame() {
    if (!scanning || !video || !canvas) return;

    const context = canvas.getContext('2d');
    if (!context) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Get image data for QR code detection
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
    
    // Simple QR code detection (this is a basic implementation)
    // In a real app, you'd use a proper QR code library
    try {
      const qrData = detectQRCode(imageData);
      if (qrData) {
        dispatch('scan', { data: qrData });
        return; // Stop scanning after successful detection
      }
    } catch (error) {
      // Continue scanning
    }

    // Continue scanning
    animationFrame = requestAnimationFrame(scanFrame);
  }

  // QR code detection using jsQR
  function detectQRCode(imageData: ImageData): string | null {
    try {
      const code = jsQR(imageData.data, imageData.width, imageData.height);
      return code ? code.data : null;
    } catch (error) {
      console.error('QR detection error:', error);
      return null;
    }
  }

  // Manual input fallback
  let manualInput = '';
  let showManualInput = false;

  function handleManualSubmit() {
    if (manualInput.trim()) {
      dispatch('scan', { data: manualInput.trim() });
      manualInput = '';
      showManualInput = false;
    }
  }
</script>

<div class="qr-scanner">
  {#if isActive}
    <div class="scanner-container">
      <video
        bind:this={video}
        class="scanner-video"
        autoplay
        muted
        playsinline
      ></video>
      
      <canvas
        bind:this={canvas}
        class="scanner-canvas"
        style="display: none;"
      ></canvas>
      
      <!-- Scanning overlay -->
      <div class="scanner-overlay">
        <div class="scan-area">
          <div class="scan-corners">
            <div class="corner top-left"></div>
            <div class="corner top-right"></div>
            <div class="corner bottom-left"></div>
            <div class="corner bottom-right"></div>
          </div>
        </div>
        
        <div class="scanner-instructions">
          <p>Position QR code within the frame</p>
          <button 
            class="manual-input-btn"
            on:click={() => showManualInput = !showManualInput}
          >
            Enter manually
          </button>
        </div>
      </div>
    </div>

    {#if showManualInput}
      <div class="manual-input">
        <input
          bind:value={manualInput}
          placeholder="Enter QR code data manually"
          class="manual-input-field"
        />
        <button on:click={handleManualSubmit} class="submit-btn">
          Submit
        </button>
      </div>
    {/if}
  {/if}
</div>

<style>
  .qr-scanner {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .scanner-container {
    position: relative;
    width: 100%;
    height: 400px;
    background: #000;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .scanner-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
  }

  .scan-area {
    width: 250px;
    height: 250px;
    position: relative;
  }

  .scan-corners {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 3px solid #fff;
  }

  .corner.top-left {
    top: 0;
    left: 0;
    border-right: none;
    border-bottom: none;
  }

  .corner.top-right {
    top: 0;
    right: 0;
    border-left: none;
    border-bottom: none;
  }

  .corner.bottom-left {
    bottom: 0;
    left: 0;
    border-right: none;
    border-top: none;
  }

  .corner.bottom-right {
    bottom: 0;
    right: 0;
    border-left: none;
    border-top: none;
  }

  .scanner-instructions {
    margin-top: 2rem;
    text-align: center;
    color: white;
  }

  .scanner-instructions p {
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .manual-input-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.8rem;
  }

  .manual-input-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  .manual-input {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .manual-input-field {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 0.25rem;
    font-size: 0.9rem;
  }

  .submit-btn {
    background: var(--color-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .submit-btn:hover {
    opacity: 0.9;
  }
</style>
