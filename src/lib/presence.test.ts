import { describe, it, expect, beforeEach, vi } from 'vitest';
import { validatePresenceRecord, getLastPresenceRecord } from './db';
import type { PresenceRecord, SchoolUser } from './types';

// Mock the database functions
vi.mock('./db', async () => {
  const actual = await vi.importActual('./db');
  return {
    ...actual,
    getById: vi.fn(),
    getLastPresenceRecord: vi.fn(),
    searchByPayload: vi.fn()
  };
});

describe('Presence Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validatePresenceRecord', () => {
    it('should validate a valid sign in record', async () => {
      // Mock user exists and belongs to school
      const mockUser: SchoolUser = {
        s: 'sch_usr',
        r: 'student',
        u: 'user-123',
        sc: 'school-456',
        n: 'Test User',
        c: 1,
        id: 'school-user-789'
      };

      const { getById } = await import('./db');
      vi.mocked(getById).mockResolvedValue(mockUser);
      vi.mocked(getLastPresenceRecord).mockResolvedValue(null); // No previous record

      const result = await validatePresenceRecord('school-user-789', 'school-456', 1);

      expect(result.valid).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it('should reject invalid user', async () => {
      const { getById } = await import('./db');
      vi.mocked(getById).mockResolvedValue(null);

      const result = await validatePresenceRecord('invalid-user', 'school-456', 1);

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('Invalid user');
    });

    it('should reject user from different school', async () => {
      const mockUser: SchoolUser = {
        s: 'sch_usr',
        r: 'student',
        u: 'user-123',
        sc: 'different-school',
        n: 'Test User',
        c: 1,
        id: 'school-user-789'
      };

      const { getById } = await import('./db');
      vi.mocked(getById).mockResolvedValue(mockUser);

      const result = await validatePresenceRecord('school-user-789', 'school-456', 1);

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('User does not belong to this school');
    });

    it('should reject duplicate consecutive sign in', async () => {
      const mockUser: SchoolUser = {
        s: 'sch_usr',
        r: 'student',
        u: 'user-123',
        sc: 'school-456',
        n: 'Test User',
        c: 1,
        id: 'school-user-789'
      };

      const mockLastRecord: PresenceRecord = {
        s: 'p',
        d: 1, // Already signed in
        t: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
        u: 'school-user-789',
        sc: 'school-456',
        id: 'presence-123'
      };

      const { getById } = await import('./db');
      vi.mocked(getById).mockResolvedValue(mockUser);
      vi.mocked(getLastPresenceRecord).mockResolvedValue(mockLastRecord);

      const result = await validatePresenceRecord('school-user-789', 'school-456', 1);

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('Cannot sign in - already signed in');
    });

    it('should reject duplicate consecutive sign out', async () => {
      const mockUser: SchoolUser = {
        s: 'sch_usr',
        r: 'student',
        u: 'user-123',
        sc: 'school-456',
        n: 'Test User',
        c: 1,
        id: 'school-user-789'
      };

      const mockLastRecord: PresenceRecord = {
        s: 'p',
        d: 0, // Already signed out
        t: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
        u: 'school-user-789',
        sc: 'school-456',
        id: 'presence-123'
      };

      const { getById } = await import('./db');
      vi.mocked(getById).mockResolvedValue(mockUser);
      vi.mocked(getLastPresenceRecord).mockResolvedValue(mockLastRecord);

      const result = await validatePresenceRecord('school-user-789', 'school-456', 0);

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('Cannot sign out - already signed out');
    });

    it('should allow alternating sign in/out', async () => {
      const mockUser: SchoolUser = {
        s: 'sch_usr',
        r: 'student',
        u: 'user-123',
        sc: 'school-456',
        n: 'Test User',
        c: 1,
        id: 'school-user-789'
      };

      const mockLastRecord: PresenceRecord = {
        s: 'p',
        d: 1, // Last was sign in
        t: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
        u: 'school-user-789',
        sc: 'school-456',
        id: 'presence-123'
      };

      const { getById } = await import('./db');
      vi.mocked(getById).mockResolvedValue(mockUser);
      vi.mocked(getLastPresenceRecord).mockResolvedValue(mockLastRecord);

      // Should allow sign out after sign in
      const result = await validatePresenceRecord('school-user-789', 'school-456', 0);

      expect(result.valid).toBe(true);
      expect(result.reason).toBeUndefined();
    });
  });
});
