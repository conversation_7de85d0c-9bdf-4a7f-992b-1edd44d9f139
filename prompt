# Presence Tracking System
By God's grace, the webapp tracks students' and staff's daily attendance.

## Presence Data Structure
New attendance object:
- s: 'p' // tenant id for attendance points
- d: 1 or 0 // direction of this attendance record, 1 means sign in, 0 means sign out
- t: timestamp
- u: user id // uuid of a `sch_usr` point
- sc: school uuid

### QR Code Generation
```js
import QrCodeWithLogo from 'qrcode-with-logos'
let qrcode = new QrCodeWithLogo({
  // canvas: document.getElementById("canvas"),
  content: uuid,
  width: 380,
  // download: true,
  image: document.getElementById('image'),
  logo: {
    src: 'https://avatars1.githubusercontent.com/u/28730619?s=460&v=4'
  }
})

// to download on 'download' click
qrcode.downloadImage('qrcode.png')
```


## Presence Pages

### Sign In Page: /sc/:school/p/i
Scans a QR code with the device camera. The QR code resolves to a UUID. By God's grace, an attendance record is created with:
- d: 1 (sign in)
- t: new Date()
- u: the UUID the QR code resolves to
- sc: the school UUID (from query param :school)

### Sign Out Page: /sc/:school/p/o
Scans a QR code with the device camera. The QR code resolves to a UUID. By God's grace, an attendance record is created with:
- d: 0 (sign out)
- t: new Date()
- u: the UUID the QR code resolves to
- sc: the school UUID (from query param :school)

## QR Code Validation
The UUID obtained from the scanned QR code must:
1. Resolve to a user/point whose 's' field (tenant_id) is 'sch_usr'
2. The sch_usr point must have a 'sc' field that matches the school UUID in the query param

## Validation Rules
Before creating a new attendance record, the system checks the last record for that user in that school by date. If the last record's type (d field) is the same as the record about to be created, it should not be allowed - it must be the opposite type.

## Error Handling
- Loading states are displayed while scanning QR codes
- On scan or record creation failure, a beautifully styled, calm, and subtle error toast notification shows the specific failure reason
- All toast notifications and warnings should be beautifully styled, calm, and subtle

## Push Notifications
Set up a service worker to handle push notifications. Server endpoint for sending notifications: `/api/push`

### User Settings Page: /u/:user/s
Only one button for now: "Enable Push Notifications". Alongside the button, provide an explanation of why it's useful to turn on push notifications for the attendance notification feature.

On clicking the button, a request is made with JS web APIs to enable push notifications for the user:
- On accept: A push notification subscription is made for the user, and its details are saved to the database using the same convention of minimal key/field names (1 or 2 characters) for database objects, with a tenant id of 'n' for notifications
- On success: A beautifully styled, calm, and subtle toast message is shown to the user
- On reject: The user is warned with a beautifully styled, calm, subtle warning modal that if they don't activate push notifications, they won't receive notifications for attendance

### Notification Feature
On sign in and sign out, a respective notification is sent to the user's phone, concisely stating the notification type and the time of the action, so it fits nicely in the push notification's usually limited practical viewing area.

## Additional Pages

### School Presence Page: /sc/:id/p
Two buttons: 'Sign Users In' and 'Sign Users Out', that navigate to `/sc/:school/p/i` and `/sc/:school/p/o` respectively.

### Get QR Code: /u/:id/qr
Creates a QR code that resolves to the UUID from query param `:id`.

### User Presence Records: /u/:user/:school/p
Shows if the user is currently signed in or signed out for that school. This is determined by getting the last record for that user for that school and checking the value of the 'd' field. If 1, signed in; if 0, signed out.

Shows all attendance records for the user for that school, with sign in and sign out times.
Each row looks like this:
```
date | sign in time | sign out time
Friday, July 6, 2001 | 08:00 | 17:00
```

Filters available:
- Date range filter
- 'Only sign ins' filter
- 'Only sign outs' filter

---

give me a plan first before executing
